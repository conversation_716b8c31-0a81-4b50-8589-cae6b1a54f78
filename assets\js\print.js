/**
 * SAM - نظام إدارة شؤون الموظفين
 * Print Management Module
 * وحدة إدارة الطباعة
 */

class PrintManager {
    constructor() {
        this.settings = Database.getSettings();
    }

    /**
     * طباعة تقرير عام
     */
    printReport(title, content, options = {}) {
        const printContent = this.generatePrintHTML(title, content, options);
        this.openPrintWindow(printContent, title);
    }

    /**
     * طباعة جدول
     */
    printTable(title, headers, rows, options = {}) {
        const tableHTML = this.generateTableHTML(headers, rows, options);
        this.printReport(title, tableHTML, options);
    }

    /**
     * طباعة قائمة الموظفين
     */
    printEmployeesList(employees, filters = {}) {
        const headers = ['الاسم', 'الرقم الوظيفي', 'القسم', 'المنصب', 'الراتب', 'تاريخ التوظيف'];
        const rows = employees.map(emp => [
            emp.name,
            emp.employee_number,
            emp.department || 'غير محدد',
            emp.position || 'غير محدد',
            window.samApp.formatCurrency(emp.salary || 0),
            window.samApp.formatDate(emp.hire_date)
        ]);

        const filterText = this.generateFilterText(filters);
        const options = {
            subtitle: filterText,
            showDate: true,
            showCompanyInfo: true
        };

        this.printTable('قائمة الموظفين', headers, rows, options);
    }

    /**
     * طباعة تقرير الحضور
     */
    printAttendanceReport(attendance, period) {
        const employees = Database.getEmployees();
        const headers = ['الموظف', 'التاريخ', 'وقت الدخول', 'وقت الخروج', 'الحالة', 'ساعات العمل'];
        
        const rows = attendance.map(record => {
            const employee = employees.find(emp => emp.id === record.employee_id);
            const workingHours = this.calculateWorkingHours(record.check_in, record.check_out);
            
            return [
                employee?.name || 'غير معروف',
                window.samApp.formatDate(record.date),
                record.check_in || '-',
                record.check_out || '-',
                this.getStatusText(record.status),
                workingHours
            ];
        });

        const options = {
            subtitle: `فترة التقرير: ${period}`,
            showDate: true,
            showCompanyInfo: true
        };

        this.printTable('تقرير الحضور والانصراف', headers, rows, options);
    }

    /**
     * طباعة تقرير الإجازات
     */
    printLeavesReport(leaves, period) {
        const employees = Database.getEmployees();
        const headers = ['الموظف', 'نوع الإجازة', 'تاريخ البداية', 'تاريخ النهاية', 'عدد الأيام', 'الحالة'];
        
        const rows = leaves.map(leave => {
            const employee = employees.find(emp => emp.id === leave.employee_id);
            
            return [
                employee?.name || 'غير معروف',
                this.getLeaveTypeText(leave.type),
                window.samApp.formatDate(leave.start_date),
                window.samApp.formatDate(leave.end_date),
                leave.days_count || 0,
                this.getLeaveStatusText(leave.status)
            ];
        });

        const options = {
            subtitle: `فترة التقرير: ${period}`,
            showDate: true,
            showCompanyInfo: true
        };

        this.printTable('تقرير الإجازات', headers, rows, options);
    }

    /**
     * طباعة كشوف الرواتب
     */
    printPayrollReport(payrolls, month) {
        const employees = Database.getEmployees();
        const headers = ['الموظف', 'الفترة/الحضور', 'الراتب الأساسي', 'البدلات', 'الإضافي', 'إجمالي الاستحقاقات', 'الخصومات', 'صافي الراتب'];

        const rows = payrolls.map(payroll => {
            const employee = employees.find(emp => emp.id === payroll.employee_id);
            const allowances = (payroll.housing_allowance || 0) + (payroll.transport_allowance || 0) + (payroll.other_allowances || 0);

            // حساب معلومات الحضور
            const attendance = Database.getAttendance({
                employee_id: payroll.employee_id,
                month: payroll.month
            });
            const workingDays = this.getWorkingDaysInMonth(payroll.month, employee);
            const attendanceDays = attendance.filter(a => a.status !== 'absent').length;

            return [
                employee?.name || 'غير معروف',
                `${attendanceDays}/${workingDays} يوم`,
                window.samApp.formatCurrency(payroll.basic_salary || 0),
                window.samApp.formatCurrency(allowances),
                window.samApp.formatCurrency(payroll.overtime || 0),
                window.samApp.formatCurrency(payroll.gross_salary || 0),
                window.samApp.formatCurrency(payroll.total_deductions || 0),
                window.samApp.formatCurrency(payroll.net_salary || 0)
            ];
        });

        // حساب الإجماليات
        const totals = payrolls.reduce((acc, payroll) => {
            acc.basicSalary += payroll.basic_salary || 0;
            acc.grossSalary += payroll.gross_salary || 0;
            acc.totalDeductions += payroll.total_deductions || 0;
            acc.netSalary += payroll.net_salary || 0;
            return acc;
        }, { basicSalary: 0, grossSalary: 0, totalDeductions: 0, netSalary: 0 });

        // إضافة صف الإجماليات
        rows.push([
            '<strong>الإجمالي</strong>',
            '',
            `<strong>${window.samApp.formatCurrency(totals.basicSalary)}</strong>`,
            '',
            '',
            `<strong>${window.samApp.formatCurrency(totals.grossSalary)}</strong>`,
            `<strong>${window.samApp.formatCurrency(totals.totalDeductions)}</strong>`,
            `<strong>${window.samApp.formatCurrency(totals.netSalary)}</strong>`
        ]);

        const options = {
            subtitle: `شهر: ${this.formatMonth(month)}`,
            showDate: true,
            showCompanyInfo: true,
            highlightLastRow: true
        };

        this.printTable('كشوف الرواتب', headers, rows, options);
    }

    /**
     * إنشاء HTML للطباعة
     */
    generatePrintHTML(title, content, options = {}) {
        const companyInfo = this.settings.company || {};
        
        return `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>${title}</title>
                <style>
                    @media print {
                        body { margin: 0; }
                        @page { margin: 1cm; }
                        .no-print { display: none; }
                    }
                    body {
                        font-family: Arial, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        direction: rtl;
                        text-align: right;
                    }
                    .header {
                        text-align: center;
                        border-bottom: 3px solid #007bff;
                        padding-bottom: 20px;
                        margin-bottom: 30px;
                    }
                    .company-name {
                        font-size: 24px;
                        font-weight: bold;
                        color: #007bff;
                        margin-bottom: 10px;
                    }
                    .report-title {
                        font-size: 20px;
                        font-weight: bold;
                        margin: 15px 0;
                    }
                    .subtitle {
                        font-size: 14px;
                        color: #666;
                        margin-bottom: 10px;
                    }
                    .print-date {
                        font-size: 12px;
                        color: #888;
                    }
                    .content {
                        margin: 20px 0;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        font-size: 12px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: right;
                    }
                    th {
                        background-color: #f8f9fa;
                        font-weight: bold;
                    }
                    .highlight-row {
                        background-color: #e9ecef;
                        font-weight: bold;
                    }
                    .footer {
                        margin-top: 40px;
                        text-align: center;
                        font-size: 10px;
                        color: #666;
                        border-top: 1px solid #ddd;
                        padding-top: 10px;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    ${options.showCompanyInfo ? `
                        <div class="company-name">${companyInfo.name || 'اسم الشركة'}</div>
                        <div class="subtitle">${companyInfo.address || ''}</div>
                        <div class="subtitle">هاتف: ${companyInfo.phone || ''} | بريد إلكتروني: ${companyInfo.email || ''}</div>
                    ` : ''}
                    <div class="report-title">${title}</div>
                    ${options.subtitle ? `<div class="subtitle">${options.subtitle}</div>` : ''}
                    ${options.showDate ? `<div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</div>` : ''}
                </div>
                
                <div class="content">
                    ${content}
                </div>
                
                <div class="footer">
                    <p>تم إنشاء هذا التقرير بواسطة نظام SAM لإدارة شؤون الموظفين</p>
                </div>
            </body>
            </html>
        `;
    }

    /**
     * إنشاء HTML للجدول
     */
    generateTableHTML(headers, rows, options = {}) {
        let tableHTML = '<table>';
        
        // إضافة رؤوس الجدول
        tableHTML += '<thead><tr>';
        headers.forEach(header => {
            tableHTML += `<th>${header}</th>`;
        });
        tableHTML += '</tr></thead>';
        
        // إضافة صفوف البيانات
        tableHTML += '<tbody>';
        rows.forEach((row, index) => {
            const isLastRow = index === rows.length - 1;
            const rowClass = (options.highlightLastRow && isLastRow) ? 'highlight-row' : '';
            
            tableHTML += `<tr class="${rowClass}">`;
            row.forEach(cell => {
                tableHTML += `<td>${cell}</td>`;
            });
            tableHTML += '</tr>';
        });
        tableHTML += '</tbody>';
        
        tableHTML += '</table>';
        return tableHTML;
    }

    /**
     * فتح نافذة الطباعة
     */
    openPrintWindow(content, title = 'طباعة') {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(content);
        printWindow.document.close();
        
        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            printWindow.print();
        };
    }

    /**
     * دوال مساعدة
     */
    calculateWorkingHours(checkIn, checkOut) {
        if (!checkIn || !checkOut) return '-';
        
        const start = new Date(`2000-01-01T${checkIn}`);
        const end = new Date(`2000-01-01T${checkOut}`);
        const diffMs = end - start;
        const diffHours = Math.round((diffMs / (1000 * 60 * 60)) * 100) / 100;
        
        return `${diffHours} ساعة`;
    }

    getStatusText(status) {
        const statusMap = {
            'present': 'حاضر',
            'absent': 'غائب',
            'late': 'متأخر',
            'early': 'مبكر'
        };
        return statusMap[status] || status;
    }

    getLeaveTypeText(type) {
        const typeMap = {
            'annual': 'إجازة سنوية',
            'sick': 'إجازة مرضية',
            'maternity': 'إجازة أمومة',
            'emergency': 'إجازة طارئة',
            'unpaid': 'إجازة بدون راتب'
        };
        return typeMap[type] || type;
    }

    getLeaveStatusText(status) {
        const statusMap = {
            'pending': 'في الانتظار',
            'approved': 'موافق عليها',
            'rejected': 'مرفوضة',
            'cancelled': 'ملغية'
        };
        return statusMap[status] || status;
    }

    formatMonth(monthStr) {
        const [year, month] = monthStr.split('-');
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return `${monthNames[parseInt(month) - 1]} ${year}`;
    }

    generateFilterText(filters) {
        const filterParts = [];
        
        if (filters.department) {
            filterParts.push(`القسم: ${filters.department}`);
        }
        if (filters.position) {
            filterParts.push(`المنصب: ${filters.position}`);
        }
        if (filters.status) {
            filterParts.push(`الحالة: ${filters.status}`);
        }
        if (filters.dateRange) {
            filterParts.push(`الفترة: ${filters.dateRange}`);
        }
        
        return filterParts.length > 0 ? `المرشحات: ${filterParts.join(' | ')}` : '';
    }

    getWorkingDaysInMonth(monthStr, employee = null) {
        const [year, month] = monthStr.split('-');
        const date = new Date(year, month - 1, 1);
        const lastDay = new Date(year, month, 0).getDate();
        let workingDays = 0;

        // Use employee-specific working days if available, otherwise use global settings
        let workingDaysList;
        if (employee && employee.working_days && employee.working_days.length > 0) {
            workingDaysList = employee.working_days;
        } else {
            const settings = Database.getSettings();
            workingDaysList = settings.working_hours?.working_days || ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'];
        }

        for (let day = 1; day <= lastDay; day++) {
            date.setDate(day);
            const dayName = date.toLocaleDateString('en-US', { weekday: 'lowercase' });
            if (workingDaysList.includes(dayName)) {
                workingDays++;
            }
        }

        return workingDays;
    }
}

// إنشاء مثيل عام للاستخدام في جميع أنحاء التطبيق
window.printManager = new PrintManager();
